<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1>个人中心</h1>
      <p>管理您的个人信息和账户设置</p>
    </div>

    <div class="profile-content">
      <a-row :gutter="24">
        <!-- 左侧个人信息卡片 -->
        <a-col :xs="24" :lg="8">
          <a-card class="profile-card">
            <div class="profile-info">
              <div class="avatar-section">
                <a-avatar :size="80" :src="userStore.user?.avatar">
                  <template #icon>
                    <UserOutlined />
                  </template>
                </a-avatar>
                <a-button 
                  type="link" 
                  @click="showAvatarModal = true"
                  class="change-avatar-btn"
                >
                  更换头像
                </a-button>
              </div>
              
              <div class="user-details">
                <h3>{{ userStore.user?.username }}</h3>
                <p class="email">{{ userStore.user?.email }}</p>
                <p class="bio">{{ userStore.user?.bio || '这个人很懒，什么都没留下...' }}</p>
                <p class="join-date">
                  <CalendarOutlined />
                  加入时间：{{ formatDate(userStore.user?.createdAt) }}
                </p>
              </div>

              <a-button 
                type="primary" 
                block 
                @click="showEditModal = true"
                :icon="h(EditOutlined)"
              >
                编辑资料
              </a-button>
            </div>
          </a-card>

          <!-- 成就统计卡片 -->
          <a-card title="我的成就" class="achievement-card">
            <a-spin :spinning="achievementLoading">
              <div class="achievement-grid">
                <div class="achievement-item">
                  <div class="achievement-number">{{ achievements?.articlesCount || 0 }}</div>
                  <div class="achievement-label">发布文章</div>
                </div>
                <div class="achievement-item">
                  <div class="achievement-number">{{ achievements?.totalLikes || 0 }}</div>
                  <div class="achievement-label">获得点赞</div>
                </div>
                <div class="achievement-item">
                  <div class="achievement-number">{{ achievements?.totalComments || 0 }}</div>
                  <div class="achievement-label">收到评论</div>
                </div>
                <div class="achievement-item">
                  <div class="achievement-number">{{ achievements?.totalFavorites || 0 }}</div>
                  <div class="achievement-label">被收藏</div>
                </div>
              </div>
            </a-spin>
          </a-card>
        </a-col>

        <!-- 右侧功能区域 -->
        <a-col :xs="24" :lg="16">
          <a-tabs v-model:activeKey="activeTab" type="card">
            <!-- 我的文章 -->
            <a-tab-pane key="articles" tab="我的文章">
              <a-spin :spinning="articlesLoading">
                <div v-if="articles.length === 0" class="empty-state">
                  <a-empty description="暂无文章" />
                </div>
                <div v-else class="articles-list">
                  <a-card 
                    v-for="article in articles" 
                    :key="article.id"
                    class="article-item"
                    :hoverable="true"
                  >
                    <div class="article-content">
                      <h4>{{ article.title }}</h4>
                      <p class="article-summary">{{ article.content?.substring(0, 100) }}...</p>
                      <div class="article-meta">
                        <span><CalendarOutlined /> {{ formatDate(article.publishDate) }}</span>
                        <span><LikeOutlined /> {{ article.likesCount }}</span>
                        <span><CommentOutlined /> {{ article.commentsCount }}</span>
                      </div>
                    </div>
                  </a-card>
                </div>
              </a-spin>
            </a-tab-pane>

            <!-- 账户设置 -->
            <a-tab-pane key="settings" tab="账户设置">
              <a-card title="安全设置">
                <a-list>
                  <a-list-item>
                    <a-list-item-meta
                      title="修改密码"
                      description="定期更换密码，保护账户安全"
                    />
                    <a-button @click="showPasswordModal = true">修改</a-button>
                  </a-list-item>
                  <a-list-item>
                    <a-list-item-meta
                      title="邮箱验证"
                      description="验证邮箱，确保账户安全"
                    />
                    <a-tag color="green">已验证</a-tag>
                  </a-list-item>
                  <a-list-item>
                    <a-list-item-meta
                      title="登录设备"
                      description="查看和管理登录设备"
                    />
                    <a-button>查看</a-button>
                  </a-list-item>
                </a-list>
              </a-card>
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
    </div>

    <!-- 编辑资料弹窗 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑个人资料"
      @ok="handleUpdateProfile"
      :confirm-loading="userStore.loading"
      width="500px"
    >
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="昵称">
          <a-input v-model:value="editForm.nickname" placeholder="请输入昵称" />
        </a-form-item>
        <a-form-item label="个人简介">
          <a-textarea 
            v-model:value="editForm.bio" 
            placeholder="介绍一下自己吧..."
            :rows="4"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 更换头像弹窗 -->
    <a-modal
      v-model:open="showAvatarModal"
      title="更换头像"
      @ok="handleUpdateAvatar"
      :confirm-loading="userStore.loading"
    >
      <div class="avatar-upload">
        <a-upload
          v-model:file-list="avatarFileList"
          :before-upload="beforeAvatarUpload"
          list-type="picture-card"
          :show-upload-list="false"
        >
          <div v-if="avatarUrl" class="avatar-preview">
            <img :src="avatarUrl" alt="avatar" />
          </div>
          <div v-else class="upload-placeholder">
            <PlusOutlined />
            <div>上传头像</div>
          </div>
        </a-upload>
        <p class="upload-tip">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
      </div>
    </a-modal>

    <!-- 修改密码弹窗 -->
    <a-modal
      v-model:open="showPasswordModal"
      title="修改密码"
      @ok="handleChangePassword"
      :confirm-loading="passwordLoading"
    >
      <a-form :model="passwordForm" :rules="passwordRules" layout="vertical">
        <a-form-item label="当前密码" name="oldPassword">
          <a-input-password v-model:value="passwordForm.oldPassword" placeholder="请输入当前密码" />
        </a-form-item>
        <a-form-item label="新密码" name="newPassword">
          <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" />
        </a-form-item>
        <a-form-item label="确认新密码" name="confirmPassword">
          <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请再次输入新密码" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue';
import { message } from 'ant-design-vue';
import { 
  UserOutlined, 
  EditOutlined, 
  CalendarOutlined,
  LikeOutlined,
  CommentOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/user';
import { userApi } from '@/api/user';
import type { UpdateUserForm, ChangePasswordForm, Achievement } from '@/types/user';

const userStore = useUserStore();

// 页面状态
const activeTab = ref('articles');
const achievementLoading = ref(false);
const articlesLoading = ref(false);
const passwordLoading = ref(false);

// 弹窗状态
const showEditModal = ref(false);
const showAvatarModal = ref(false);
const showPasswordModal = ref(false);

// 数据
const achievements = ref<Achievement | null>(null);
const articles = ref<any[]>([]);

// 编辑表单
const editForm = reactive({
  nickname: '',
  bio: '',
});

// 头像上传
const avatarFileList = ref([]);
const avatarUrl = ref('');
const avatarFile = ref<File | null>(null);

// 密码修改表单
const passwordForm = reactive<ChangePasswordForm>({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// 密码验证规则
const passwordRules = {
  oldPassword: [{ required: true, message: '请输入当前密码' }],
  newPassword: [
    { required: true, message: '请输入新密码' },
    { min: 6, message: '密码长度至少6位' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (_: any, value: string) => {
        if (value !== passwordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
    },
  ],
};

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('zh-CN');
};

// 加载用户成就
const loadAchievements = async () => {
  if (!userStore.user?.email) return;
  
  try {
    achievementLoading.value = true;
    const response = await userApi.getUserAchievements(userStore.user.email);
    if (response.success) {
      achievements.value = response.data;
    }
  } catch (error) {
    console.error('加载成就失败:', error);
  } finally {
    achievementLoading.value = false;
  }
};

// 加载用户文章
const loadArticles = async () => {
  if (!userStore.user?.email) return;
  
  try {
    articlesLoading.value = true;
    const response = await userApi.getUserArticles(userStore.user.email);
    if (response.success) {
      articles.value = response.data || [];
    }
  } catch (error) {
    console.error('加载文章失败:', error);
  } finally {
    articlesLoading.value = false;
  }
};

// 初始化编辑表单
const initEditForm = () => {
  if (userStore.user) {
    editForm.nickname = userStore.user.username;
    editForm.bio = userStore.user.bio;
  }
};

// 处理头像上传前的验证
const beforeAvatarUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!');
    return false;
  }
  
  // 预览图片
  const reader = new FileReader();
  reader.onload = (e) => {
    avatarUrl.value = e.target?.result as string;
  };
  reader.readAsDataURL(file);
  
  avatarFile.value = file;
  return false; // 阻止自动上传
};

// 更新个人资料
const handleUpdateProfile = async () => {
  if (!userStore.user?.email) return;
  
  const updateData: UpdateUserForm = {
    email: userStore.user.email,
    nickname: editForm.nickname,
    bio: editForm.bio,
  };
  
  const success = await userStore.updateUserInfo(updateData);
  if (success) {
    showEditModal.value = false;
  }
};

// 更新头像
const handleUpdateAvatar = async () => {
  if (!userStore.user?.email || !avatarFile.value) {
    message.error('请选择头像文件');
    return;
  }
  
  const updateData: UpdateUserForm = {
    email: userStore.user.email,
    nickname: userStore.user.username,
    bio: userStore.user.bio,
    avatar: avatarFile.value,
  };
  
  const success = await userStore.updateUserInfo(updateData);
  if (success) {
    showAvatarModal.value = false;
    avatarUrl.value = '';
    avatarFile.value = null;
  }
};

// 修改密码
const handleChangePassword = async () => {
  // 这里需要实现密码修改的API调用
  // 由于后端没有提供密码修改接口，这里只是模拟
  passwordLoading.value = true;
  
  setTimeout(() => {
    passwordLoading.value = false;
    message.success('密码修改成功');
    showPasswordModal.value = false;
    // 重置表单
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
  }, 1000);
};

// 页面初始化
onMounted(() => {
  if (!userStore.user) {
    userStore.initUser();
  }
  
  if (userStore.user) {
    initEditForm();
    loadAchievements();
    loadArticles();
  }
});
</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.profile-header {
  text-align: center;
  margin-bottom: 32px;
}

.profile-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.profile-header p {
  color: #6b7280;
  font-size: 16px;
}

.profile-card {
  margin-bottom: 24px;
}

.profile-info {
  text-align: center;
}

.avatar-section {
  margin-bottom: 24px;
}

.change-avatar-btn {
  display: block;
  margin: 8px auto 0;
  padding: 0;
}

.user-details h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.user-details .email {
  color: #6b7280;
  margin-bottom: 8px;
}

.user-details .bio {
  color: #374151;
  margin-bottom: 16px;
  font-style: italic;
}

.user-details .join-date {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 24px;
}

.achievement-card {
  margin-bottom: 24px;
}

.achievement-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.achievement-item {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.achievement-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.achievement-label {
  font-size: 14px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 48px 0;
}

.articles-list {
  display: grid;
  gap: 16px;
}

.article-item {
  transition: all 0.3s ease;
}

.article-content h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
}

.article-summary {
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.5;
}

.article-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #9ca3af;
}

.avatar-upload {
  text-align: center;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.upload-tip {
  margin-top: 8px;
  color: #6b7280;
  font-size: 12px;
}

@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }
  
  .achievement-grid {
    grid-template-columns: 1fr;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
