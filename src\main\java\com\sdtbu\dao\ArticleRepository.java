package com.sdtbu.dao;

import com.sdtbu.pojo.Article;
import com.sdtbu.pojo.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ArticleRepository extends JpaRepository<Article, Integer> {

    // 获取最新博客（按发布时间排序）
    @Query("SELECT a FROM Article a ORDER BY a.publishDate DESC")
    List<Article> findTop5ByOrderByPublishDateDesc();

    // 获取热度排行榜博客（按点赞数、评论数和收藏数排序）
    @Query("SELECT a FROM Article a ORDER BY (a.likesCount + a.commentsCount+ a.favoritesCount) DESC")
    List<Article> findTop5ByOrderByHotnessDesc();

    List<Article> findByUser(User user);

    // 查询用户发表的文章数
    long countByUserEmail(String email);

    // 查询用户收到的所有点赞数
    @Query("SELECT SUM(a.likesCount) FROM Article a WHERE a.user.email = :email")
    int countTotalLikesByUserEmail(@Param("email") String email);

    // 查询用户所有文章的评论数
    @Query("SELECT SUM(a.commentsCount) FROM Article a WHERE a.user.email = :email")
    int countTotalCommentsByUserEmail(@Param("email") String email);

    // 查询用户所有文章的收藏数
    @Query("SELECT SUM(a.favoritesCount) FROM Article a WHERE a.user.email = :email")
    int countTotalFavoritesByUserEmail(@Param("email") String email);

    @Query("SELECT a FROM Article a WHERE a.user.email = :email ORDER BY a.likesCount DESC, a.commentsCount DESC")
    List<Article> findPopularArticlesByUserEmail(@Param("email") String email);

    Article findById(Long articleId);
}

