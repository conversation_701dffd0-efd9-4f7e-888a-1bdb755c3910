// 用户相关API
import request from './request';
import type { 
  User, 
  LoginForm, 
  RegisterForm, 
  CaptchaLoginForm, 
  UpdateUserForm,
  ApiResponse,
  Achievement 
} from '@/types/user';

export const userApi = {
  // 用户注册
  register(data: { email: string; password: string }): Promise<ApiResponse<User>> {
    return request.post('/user/register', data);
  },

  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<User>> {
    return request.post('/user/login', {
      email: data.email,
      password: data.password
    });
  },

  // 验证码登录
  loginWithCaptcha(data: CaptchaLoginForm): Promise<ApiResponse<User>> {
    return request.post('/user/login/captcha', data);
  },

  // 检查用户是否存在
  checkUser(email: string): Promise<ApiResponse<{ exists: boolean }>> {
    return request.post('/user/check-user', { email });
  },

  // 更新用户信息
  updateUser(data: UpdateUserForm): Promise<ApiResponse<string>> {
    const formData = new FormData();
    formData.append('email', data.email);
    formData.append('nickname', data.nickname);
    formData.append('bio', data.bio);
    
    if (data.avatar) {
      formData.append('avatar', data.avatar);
    }

    return request.put('/user/update', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 获取用户文章
  getUserArticles(email: string): Promise<ApiResponse<any[]>> {
    return request.get('/user/articles', { params: { email } });
  },

  // 获取用户成就
  getUserAchievements(email: string): Promise<ApiResponse<Achievement>> {
    return request.get('/user/achievements', { params: { email } });
  },
};

export const codeApi = {
  // 发送验证码
  sendCode(email: string): Promise<ApiResponse<null>> {
    return request.post('/code/send-code', { email });
  },

  // 验证验证码
  verifyCode(email: string, code: string): Promise<ApiResponse<null>> {
    return request.post('/code/verify-code', { email, code });
  },
};
