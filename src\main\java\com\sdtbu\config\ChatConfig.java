package com.sdtbu.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class ChatConfig {

    @Bean
    public InMemoryChatMemoryRepository chatMemoryRepository() { // Renamed for clarity
        return new InMemoryChatMemoryRepository();
    }

    @Bean
    public ChatClient chatClient(OpenAiChatModel model, ChatMemory chatMemory) {
        return ChatClient
                .builder(model)
                .defaultSystem("你是一个关于个人博客的专家，擅长回答关于个人博客的各种问题,用户提出主题后可以帮用户进行文案的撰写," +
                        "但是用户需要提供足够的上下文信息,你可以根据用户提供的上下文信息进行回答,如果用户没有提供足够的上下文信息,你可以提示用户提供更多的信息,如果用户询问与个人博客无关的问题,你可以提示用户询问与个人博客相关的问题")
                .defaultAdvisors(
                        new SimpleLoggerAdvisor(),
                        MessageChatMemoryAdvisor.builder(chatMemory).build()
                )
                .build();
    }
}