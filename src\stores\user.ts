// 用户状态管理
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { userApi, codeApi } from '@/api/user';
import type { User, LoginForm, RegisterForm, CaptchaLoginForm, UpdateUserForm } from '@/types/user';

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null);
  const token = ref<string>('');
  const loading = ref(false);

  // 计算属性
  const isLoggedIn = computed(() => !!user.value && !!token.value);
  const userInfo = computed(() => user.value);

  // 初始化用户信息（从localStorage恢复）
  const initUser = () => {
    const savedUser = localStorage.getItem('user');
    const savedToken = localStorage.getItem('token');
    
    if (savedUser && savedToken) {
      try {
        user.value = JSON.parse(savedUser);
        token.value = savedToken;
      } catch (error) {
        console.error('解析用户信息失败:', error);
        clearUser();
      }
    }
  };

  // 清除用户信息
  const clearUser = () => {
    user.value = null;
    token.value = '';
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  };

  // 保存用户信息
  const saveUser = (userData: User, userToken?: string) => {
    user.value = userData;
    if (userToken) {
      token.value = userToken;
      localStorage.setItem('token', userToken);
    }
    localStorage.setItem('user', JSON.stringify(userData));
  };

  // 用户登录
  const login = async (loginForm: LoginForm) => {
    try {
      loading.value = true;
      const response = await userApi.login(loginForm);
      
      if (response.success && response.data) {
        // 生成一个简单的token（实际项目中应该由后端返回）
        const userToken = `token_${response.data.id}_${Date.now()}`;
        saveUser(response.data, userToken);
        
        // 如果选择记住登录状态，设置更长的过期时间
        if (loginForm.remember) {
          localStorage.setItem('rememberLogin', 'true');
        }
        
        message.success('登录成功');
        return response.data;
      }
    } catch (error: any) {
      message.error(error.message || '登录失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 验证码登录
  const loginWithCaptcha = async (loginForm: CaptchaLoginForm) => {
    try {
      loading.value = true;
      const response = await userApi.loginWithCaptcha(loginForm);
      
      if (response.success && response.data) {
        const userToken = `token_${response.data.id}_${Date.now()}`;
        saveUser(response.data, userToken);
        message.success('登录成功');
        return response.data;
      }
    } catch (error: any) {
      message.error(error.message || '登录失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 用户注册
  const register = async (registerForm: RegisterForm) => {
    try {
      loading.value = true;
      const response = await userApi.register({
        email: registerForm.email,
        password: registerForm.password
      });
      
      if (response.success && response.data) {
        message.success('注册成功');
        return response.data;
      }
    } catch (error: any) {
      message.error(error.message || '注册失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 发送验证码
  const sendVerificationCode = async (email: string) => {
    try {
      loading.value = true;
      const response = await codeApi.sendCode(email);
      
      if (response.success) {
        message.success('验证码已发送到您的邮箱');
        return true;
      }
      return false;
    } catch (error: any) {
      message.error(error.message || '发送验证码失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 更新用户信息
  const updateUserInfo = async (updateForm: UpdateUserForm) => {
    try {
      loading.value = true;
      const response = await userApi.updateUser(updateForm);
      
      if (response.success && user.value) {
        // 更新本地用户信息
        user.value.username = updateForm.nickname;
        user.value.bio = updateForm.bio;
        localStorage.setItem('user', JSON.stringify(user.value));
        message.success('用户信息更新成功');
        return true;
      }
      return false;
    } catch (error: any) {
      message.error(error.message || '更新用户信息失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 用户登出
  const logout = () => {
    clearUser();
    message.success('已退出登录');
  };

  // 检查用户是否存在
  const checkUserExists = async (email: string) => {
    try {
      const response = await userApi.checkUser(email);
      return response.success ? response.data.exists : false;
    } catch (error) {
      return false;
    }
  };

  return {
    // 状态
    user: userInfo,
    token,
    loading,
    isLoggedIn,
    
    // 方法
    initUser,
    login,
    loginWithCaptcha,
    register,
    logout,
    sendVerificationCode,
    updateUserInfo,
    checkUserExists,
    clearUser,
  };
});
