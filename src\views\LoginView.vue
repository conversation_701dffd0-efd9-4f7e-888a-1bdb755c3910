<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>用户登录</h1>
        <p>欢迎回来，请登录您的账户</p>
      </div>

      <a-tabs v-model:activeKey="activeTab" centered>
        <!-- 密码登录 -->
        <a-tab-pane key="password" tab="密码登录">
          <a-form
            :model="passwordForm"
            :rules="passwordRules"
            @finish="handlePasswordLogin"
            layout="vertical"
            class="login-form"
          >
            <a-form-item label="邮箱" name="email">
              <a-input
                v-model:value="passwordForm.email"
                placeholder="请输入邮箱"
                size="large"
                :prefix="h(MailOutlined)"
              />
            </a-form-item>

            <a-form-item label="密码" name="password">
              <a-input-password
                v-model:value="passwordForm.password"
                placeholder="请输入密码"
                size="large"
                :prefix="h(LockOutlined)"
              />
            </a-form-item>

            <a-form-item>
              <div class="login-options">
                <a-checkbox v-model:checked="passwordForm.remember">
                  记住登录状态
                </a-checkbox>
                <a-button type="link" @click="$router.push('/forgot-password')">
                  忘记密码？
                </a-button>
              </div>
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="userStore.loading"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>

        <!-- 验证码登录 -->
        <a-tab-pane key="captcha" tab="验证码登录">
          <a-form
            :model="captchaForm"
            :rules="captchaRules"
            @finish="handleCaptchaLogin"
            layout="vertical"
            class="login-form"
          >
            <a-form-item label="邮箱" name="email">
              <a-input
                v-model:value="captchaForm.email"
                placeholder="请输入邮箱"
                size="large"
                :prefix="h(MailOutlined)"
              />
            </a-form-item>

            <a-form-item label="验证码" name="captcha">
              <div class="captcha-input">
                <a-input
                  v-model:value="captchaForm.captcha"
                  placeholder="请输入验证码"
                  size="large"
                  :prefix="h(SafetyOutlined)"
                />
                <a-button
                  :disabled="countdown > 0"
                  :loading="sendingCode"
                  @click="sendCaptcha"
                  size="large"
                >
                  {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
                </a-button>
              </div>
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="userStore.loading"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>

      <div class="login-footer">
        <span>还没有账户？</span>
        <a-button type="link" @click="$router.push('/register')">
          立即注册
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { MailOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/user';
import type { LoginForm, CaptchaLoginForm } from '@/types/user';

const router = useRouter();
const userStore = useUserStore();

// 当前激活的标签页
const activeTab = ref('password');

// 密码登录表单
const passwordForm = reactive<LoginForm>({
  email: '',
  password: '',
  remember: false,
});

// 验证码登录表单
const captchaForm = reactive<CaptchaLoginForm>({
  email: '',
  captcha: '',
});

// 发送验证码相关状态
const sendingCode = ref(false);
const countdown = ref(0);

// 表单验证规则
const passwordRules = {
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度至少6位' },
  ],
};

const captchaRules = {
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' },
  ],
  captcha: [
    { required: true, message: '请输入验证码' },
    { len: 6, message: '验证码为6位数字' },
  ],
};

// 密码登录
const handlePasswordLogin = async () => {
  try {
    await userStore.login(passwordForm);
    router.push('/profile');
  } catch (error) {
    // 错误已在store中处理
  }
};

// 验证码登录
const handleCaptchaLogin = async () => {
  try {
    await userStore.loginWithCaptcha(captchaForm);
    router.push('/profile');
  } catch (error) {
    // 错误已在store中处理
  }
};

// 发送验证码
const sendCaptcha = async () => {
  if (!captchaForm.email) {
    message.error('请先输入邮箱');
    return;
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(captchaForm.email)) {
    message.error('请输入正确的邮箱格式');
    return;
  }

  sendingCode.value = true;
  const success = await userStore.sendVerificationCode(captchaForm.email);
  sendingCode.value = false;

  if (success) {
    // 开始倒计时
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  }
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.login-header p {
  color: #6b7280;
  font-size: 14px;
}

.login-form {
  margin-top: 24px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.captcha-input {
  display: flex;
  gap: 12px;
}

.captcha-input .ant-input {
  flex: 1;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  color: #6b7280;
}

@media (max-width: 480px) {
  .login-card {
    padding: 24px;
  }
  
  .captcha-input {
    flex-direction: column;
  }
  
  .captcha-input .ant-btn {
    width: 100%;
  }
}
</style>
