<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>创建您的账户，开始精彩旅程</p>
      </div>

      <a-form
        :model="registerForm"
        :rules="registerRules"
        @finish="handleRegister"
        layout="vertical"
        class="register-form"
      >
        <a-form-item label="邮箱" name="email">
          <a-input
            v-model:value="registerForm.email"
            placeholder="请输入邮箱"
            size="large"
            :prefix="h(MailOutlined)"
            @blur="checkEmailExists"
          />
          <div v-if="emailCheckResult" class="email-check-result">
            <span v-if="emailCheckResult.exists" class="error">
              <ExclamationCircleOutlined /> 该邮箱已被注册
            </span>
            <span v-else class="success">
              <CheckCircleOutlined /> 邮箱可用
            </span>
          </div>
        </a-form-item>

        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="registerForm.password"
            placeholder="请输入密码"
            size="large"
            :prefix="h(LockOutlined)"
          />
          <div class="password-strength">
            <div class="strength-bar">
              <div 
                class="strength-fill" 
                :class="passwordStrength.level"
                :style="{ width: passwordStrength.width }"
              ></div>
            </div>
            <span class="strength-text">{{ passwordStrength.text }}</span>
          </div>
        </a-form-item>

        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="registerForm.confirmPassword"
            placeholder="请再次输入密码"
            size="large"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item label="验证码" name="captcha">
          <div class="captcha-input">
            <a-input
              v-model:value="registerForm.captcha"
              placeholder="请输入验证码"
              size="large"
              :prefix="h(SafetyOutlined)"
            />
            <a-button
              :disabled="countdown > 0 || !registerForm.email || emailCheckResult?.exists"
              :loading="sendingCode"
              @click="sendCaptcha"
              size="large"
            >
              {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
            </a-button>
          </div>
        </a-form-item>

        <a-form-item name="agreement">
          <a-checkbox v-model:checked="registerForm.agreement">
            我已阅读并同意
            <a-button type="link" @click="showTerms = true">用户协议</a-button>
            和
            <a-button type="link" @click="showPrivacy = true">隐私政策</a-button>
          </a-checkbox>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="userStore.loading"
          >
            注册
          </a-button>
        </a-form-item>
      </a-form>

      <div class="register-footer">
        <span>已有账户？</span>
        <a-button type="link" @click="$router.push('/login')">
          立即登录
        </a-button>
      </div>
    </div>

    <!-- 用户协议弹窗 -->
    <a-modal
      v-model:open="showTerms"
      title="用户协议"
      :footer="null"
      width="600px"
    >
      <div class="terms-content">
        <h3>用户协议</h3>
        <p>欢迎使用我们的服务。请仔细阅读以下条款...</p>
        <!-- 这里可以添加完整的用户协议内容 -->
      </div>
    </a-modal>

    <!-- 隐私政策弹窗 -->
    <a-modal
      v-model:open="showPrivacy"
      title="隐私政策"
      :footer="null"
      width="600px"
    >
      <div class="privacy-content">
        <h3>隐私政策</h3>
        <p>我们重视您的隐私。本政策说明我们如何收集、使用和保护您的信息...</p>
        <!-- 这里可以添加完整的隐私政策内容 -->
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h, watch } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
  MailOutlined, 
  LockOutlined, 
  SafetyOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/user';
import type { RegisterForm } from '@/types/user';

const router = useRouter();
const userStore = useUserStore();

// 注册表单
const registerForm = reactive<RegisterForm & { agreement: boolean }>({
  email: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreement: false,
});

// 发送验证码相关状态
const sendingCode = ref(false);
const countdown = ref(0);

// 邮箱检查结果
const emailCheckResult = ref<{ exists: boolean } | null>(null);

// 弹窗状态
const showTerms = ref(false);
const showPrivacy = ref(false);

// 密码强度计算
const passwordStrength = computed(() => {
  const password = registerForm.password;
  if (!password) return { level: '', width: '0%', text: '' };

  let score = 0;
  let text = '';

  // 长度检查
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;

  // 复杂度检查
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  if (score <= 2) {
    return { level: 'weak', width: '33%', text: '弱' };
  } else if (score <= 4) {
    return { level: 'medium', width: '66%', text: '中' };
  } else {
    return { level: 'strong', width: '100%', text: '强' };
  }
});

// 表单验证规则
const registerRules = {
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度至少6位' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (_: any, value: string) => {
        if (value !== registerForm.password) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
    },
  ],
  captcha: [
    { required: true, message: '请输入验证码' },
    { len: 6, message: '验证码为6位数字' },
  ],
  agreement: [
    {
      validator: (_: any, value: boolean) => {
        if (!value) {
          return Promise.reject('请阅读并同意用户协议和隐私政策');
        }
        return Promise.resolve();
      },
    },
  ],
};

// 检查邮箱是否存在
const checkEmailExists = async () => {
  if (!registerForm.email) {
    emailCheckResult.value = null;
    return;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(registerForm.email)) {
    emailCheckResult.value = null;
    return;
  }

  try {
    const exists = await userStore.checkUserExists(registerForm.email);
    emailCheckResult.value = { exists };
  } catch (error) {
    emailCheckResult.value = null;
  }
};

// 发送验证码
const sendCaptcha = async () => {
  if (!registerForm.email) {
    message.error('请先输入邮箱');
    return;
  }

  if (emailCheckResult.value?.exists) {
    message.error('该邮箱已被注册');
    return;
  }

  sendingCode.value = true;
  const success = await userStore.sendVerificationCode(registerForm.email);
  sendingCode.value = false;

  if (success) {
    // 开始倒计时
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  }
};

// 处理注册
const handleRegister = async () => {
  try {
    await userStore.register(registerForm);
    message.success('注册成功，请登录');
    router.push('/login');
  } catch (error) {
    // 错误已在store中处理
  }
};

// 监听邮箱变化，清除检查结果
watch(() => registerForm.email, () => {
  emailCheckResult.value = null;
});
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.register-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.register-header p {
  color: #6b7280;
  font-size: 14px;
}

.register-form {
  margin-top: 24px;
}

.email-check-result {
  margin-top: 4px;
  font-size: 12px;
}

.email-check-result .success {
  color: #52c41a;
}

.email-check-result .error {
  color: #ff4d4f;
}

.password-strength {
  margin-top: 4px;
}

.strength-bar {
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.strength-fill.weak {
  background-color: #ff4d4f;
}

.strength-fill.medium {
  background-color: #faad14;
}

.strength-fill.strong {
  background-color: #52c41a;
}

.strength-text {
  font-size: 12px;
  color: #6b7280;
  margin-left: 4px;
}

.captcha-input {
  display: flex;
  gap: 12px;
}

.captcha-input .ant-input {
  flex: 1;
}

.register-footer {
  text-align: center;
  margin-top: 24px;
  color: #6b7280;
}

.terms-content,
.privacy-content {
  max-height: 400px;
  overflow-y: auto;
}

@media (max-width: 480px) {
  .register-card {
    padding: 24px;
  }
  
  .captcha-input {
    flex-direction: column;
  }
  
  .captcha-input .ant-btn {
    width: 100%;
  }
}
</style>
