package com.sdtbu.dao;

import com.sdtbu.pojo.Comment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {

    Integer countByArticleId(Long articleId);

    // 根据文章 ID 获取最新评论（按创建时间降序）
    @Query("SELECT c FROM Comment c WHERE c.article.id = :articleId ORDER BY c.createdAt DESC")
    List<Comment> findLatestCommentsByArticleId(Long articleId);

}
