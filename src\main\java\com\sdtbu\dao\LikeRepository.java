package com.sdtbu.dao;

import com.sdtbu.pojo.Like;
import com.sdtbu.pojo.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LikeRepository extends JpaRepository<Like, Long> {
    Integer findByArticleId(Long articleId);

    Integer countByArticleId(Long articleId);

    Optional<Like> findByArticleIdAndUserEmail(Long articleId, String email);

    List<Like> findByUser(User user);

    void deleteByArticleId(Long articleId);
}
