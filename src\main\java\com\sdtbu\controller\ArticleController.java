package com.sdtbu.controller;

import com.sdtbu.dao.LikeRepository;
import com.sdtbu.pojo.Article;
import com.sdtbu.pojo.Like;
import com.sdtbu.pojo.User;
import com.sdtbu.service.ArticleService;
import com.sdtbu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.util.*;
@CrossOrigin
@RestController
@RequestMapping("/article")
public class ArticleController {


    @Autowired
    private UserService userService;

    @Autowired
    private ArticleService articleService;
    @Autowired
    private LikeRepository likeRepository;
    @Value("${upload.dir}")
    private final String uploadDir = "C:\\Users\\<USER>\\Desktop\\Blog01\\img";


    // 获取最新的文章
    @GetMapping("/latest")
    public List<Article> getLatestBlogs() {
        return articleService.getLatestBlogs();
    }

    // 获取点赞最多的文章
    @GetMapping("/top")
    public List<Article> getTopBlogs() {
        return articleService.getTopBlogs();
    }

    // 获取文章详情，根据文章 id
    @GetMapping("/{id}")
    public Article getArticleDetails(@PathVariable Integer id) {  // 使用 Integer 类型
        return articleService.getArticleDetails(Long.valueOf(id));
    }

    @PostMapping("/publish")
    public String publishArticle(@RequestParam String title,
                                 @RequestParam String content,
                                 @RequestParam String email,  // 传递邮箱
                                 @RequestParam(required = false) MultipartFile image) {
        try {
            if (title == null || title.isEmpty() || content == null || content.isEmpty()) {
                throw new IllegalArgumentException("标题或内容不能为空");
            }

            if (email == null || email.isEmpty()) {
                throw new IllegalArgumentException("用户邮箱不能为空");
            }

            // 查询用户
            User user = userService.findByEmail(email);
            if (user == null) {
                throw new IllegalArgumentException("用户未找到");
            }

            // 创建文章对象并设置属性
            Article article = new Article();
            article.setTitle(title);
            article.setContent(content);
            article.setUser(user);  // 关联 User
            article.setEmail(email);  // 设置邮箱

            // 文章的其他设置...
            article.setPublishDate(new Timestamp(System.currentTimeMillis()));
            article.setLikesCount(0);
            article.setCommentsCount(0);
            article.setFavoritesCount(0);

            if (image != null && !image.isEmpty()) {
                String fileName = StringUtils.cleanPath(Objects.requireNonNull(image.getOriginalFilename()));
                Path path = Paths.get(uploadDir, fileName);
                Files.copy(image.getInputStream(), path);
                article.setImage("/img/" + fileName);
                System.out.println(article.getImage());
            }

            // 保存文章
            articleService.saveArticle(article);

            return "文章已发布";

        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            return "发布文章失败：" + e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
            return "发布文章失败：系统错误";
        }
    }

    @PostMapping("/update")
    public ResponseEntity<String> updateArticle(@RequestParam("id") Long id,
                                                @RequestParam String title,
                                                @RequestParam String content,
                                                @RequestParam(required = false) MultipartFile image,
                                                @RequestParam("updateImage") String updateImage) {
        try {
            // 查找现有的文章
            Article article = articleService.findById(id);
            if (article == null) {
                return ResponseEntity.badRequest().body("文章未找到");
            }

            // 更新文章内容
            article.setTitle(title);
            article.setContent(content);

            // 如果前端传来了新图片，处理文件上传
            if ("true".equals(updateImage) && image != null && !image.isEmpty()) {
                // 处理图片上传
                String fileName = StringUtils.cleanPath(Objects.requireNonNull(image.getOriginalFilename()));

                if (fileName == null || fileName.isEmpty()) {
                    return ResponseEntity.badRequest().body("上传的图片文件无效");
                }

                // 删除旧的图片（如果存在）
                if (article.getImage() != null) {
                    Path oldImagePath = Paths.get(uploadDir, article.getImage().replace("/img/", ""));
                    Files.deleteIfExists(oldImagePath);
                }

                // 保存新图片到本地
                Path path = Paths.get(uploadDir, fileName);
                Files.copy(image.getInputStream(), path);

                // 更新文章的图片路径
                article.setImage("/img/" + fileName);

            } else if ("false".equals(updateImage)) {
                // 如果不更新图片，可以保持原图片
                System.out.println("保持原有图片不变");
            }

            // 保存更新后的文章
            articleService.saveArticle(article);

            return ResponseEntity.ok("文章已更新");

        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("更新文章失败: " + e.getMessage());
        }
    }
    // 删除文章
    @PostMapping("/delete")
    public ResponseEntity<String> deleteArticle(@RequestBody Article article) {
        try {
            // 调用服务层的删除方法
            articleService.deleteArticle(article.getId());
            System.out.println(article.getId());

            // 返回成功响应
            return ResponseEntity.ok("文章已删除");
        } catch (IllegalArgumentException e) {
            // 如果文章不存在，返回错误响应
            return ResponseEntity.status(404).body("文章不存在");
        } catch (Exception e) {
            // 处理其他异常
            return ResponseEntity.status(500).body("删除文章失败");
        }
    }
    // 获取作者的热门文章
    @GetMapping("/popular-articles")
    public ResponseEntity<List<Article>> getPopularArticles(@RequestParam String email) {
        try {
            List<Article> popularArticles = articleService.getPopularArticlesByUserEmail(email);
            return ResponseEntity.ok(popularArticles);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(null);
        }
    }


    // 点赞文章
    @PostMapping("/{articleId}/like")
    public ResponseEntity<Article> likeArticle(@PathVariable Long articleId,
                                               @RequestParam String email) {
        try {
            Article updatedArticle = articleService.likeArticle(articleId, email);

            return ResponseEntity.ok(updatedArticle);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
    }

    // 获取文章的点赞状态
    @GetMapping("/{articleId}/like-status")
    public ResponseEntity<Map<String, Object>> getLikeStatus(@PathVariable Long articleId,
                                                             @RequestParam String email) {
        Map<String, Object> response = new HashMap<>();
        Optional<Like> like = likeRepository.findByArticleIdAndUserEmail(articleId, email);
        response.put("liked", like.isPresent());
        return ResponseEntity.ok(response);
    }
    // 获取文章的收藏状态
    @GetMapping("/{id}/favorite-status")
    public ResponseEntity<Map<String, Object>> getFavoriteStatus(
            @RequestParam String email,
            @PathVariable Long id) {
        try {
            // 获取用户和文章
            User user = userService.findByEmail(email);
            Article article = articleService.findById(id);

            if (user == null || article == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", "User or article not found"));
            }

            // 判断用户是否收藏了该文章
            boolean favorited = articleService.isArticleFavoritedByUser(article, user);

            // 返回收藏状态
            Map<String, Object> response = new HashMap<>();
            response.put("favorited", favorited);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message", "Error retrieving favorite status"));
        }
    }
    @PostMapping("/{articleId}/favorite")
    public ResponseEntity<Map<String, Object>> favoriteArticle(@PathVariable Long articleId,
                                                               @RequestParam String email) {
        try {
            // 获取用户和文章
            User user = userService.findByEmail(email);
            Article article = articleService.findById(articleId);

            if (user == null || article == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", "User or article not found"));
            }

            // 判断用户是否已收藏该文章
            if (!articleService.isArticleFavoritedByUser(article, user)) {
                // 用户未收藏，进行收藏
                articleService.addFavorite(article, user);
            } else {
                // 用户已收藏，进行取消收藏
                articleService.removeFavorite(article, user);
            }

            // 返回更新后的收藏数
            Map<String, Object> response = new HashMap<>();
            response.put("favoritesCount", article.getFavoritesCount());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("message", "Error processing favorite/unfavorite"));
        }
    }
}


