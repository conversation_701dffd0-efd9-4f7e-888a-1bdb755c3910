package com.sdtbu.service;

import com.sdtbu.dao.*;
import com.sdtbu.pojo.Article;
import com.sdtbu.pojo.Favorite;
import com.sdtbu.pojo.Like;
import com.sdtbu.pojo.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Service
public class ArticleService {


    @Autowired
    private ArticleRepository articleRepository;

    @Autowired
    private CommentRepository commentRepository;

    @Autowired
    private LikeRepository likeRepository;

    @Autowired
    private FavoriteRepository favoriteRepository;

    @Autowired
    private UserRepository userRepository; // 用于获取用户信息
    private static final Logger logger = LoggerFactory.getLogger(ArticleService.class);

    // 获取最新博客，按发布时间降序排列
    public List<Article> getLatestBlogs() {
        return articleRepository.findTop5ByOrderByPublishDateDesc();
    }

    // 获取热度排行榜博客，按热度降序排列
    public List<Article> getTopBlogs() {
        return articleRepository.findTop5ByOrderByHotnessDesc();
    }

    // 获取文章详情，包括评论数、点赞数、收藏数
    public Article getArticleDetails(Long articleId) {
        Optional<Article> article = articleRepository.findById(Math.toIntExact(articleId));
        if (article.isPresent()) {
            Article a = article.get();

            // 设置评论数、点赞数、收藏数
            a.setLikesCount(likeRepository.countByArticleId(articleId));          // 点赞数
            a.setFavoritesCount(favoriteRepository.countByArticleId(articleId)); // 收藏数

            return a;
        }
        return null;  // 文章不存在时返回 null
    }

    public Article saveArticle(Article article) {
        // 获取传递的用户对象（article.getUser()）
        User user = article.getUser();

        if (user == null || user.getEmail() == null || user.getEmail().isEmpty()) {
            throw new IllegalArgumentException("用户邮箱不能为空");
        }

        // 设置文章的用户
        article.setUser(user);

        // 通过用户获取邮箱并设置到文章中
        article.setEmail(user.getEmail());

        // 设置发布日期
        article.setPublishDate(new java.sql.Timestamp(System.currentTimeMillis()));

        // 初始化评论数、点赞数、收藏数为 0
        article.setCommentsCount(0);
        article.setLikesCount(0);
        article.setFavoritesCount(0);

//        // 如果文章中包含图片路径，保存图片路径
//        if (article.getImage() != null && !article.getImage().isEmpty()) {
//            // 假设图片路径已正确设置为 /img/xxx
//            article.setImage("/img/" + article.getImage());
//            System.out.println(article.getImage());
//        }

        // 保存文章到数据库
        return articleRepository.save(article);
    }


    // 根据用户查找该用户的所有文章
    public List<Article> findByUser(User user) {
        return articleRepository.findByUser(user);
    }
    public void deleteArticle(Long articleId) {
        Article article = articleRepository.findById(Math.toIntExact(articleId))
                .orElseThrow(() -> new IllegalArgumentException("文章不存在"));

        // Delete likes
        logger.info("Deleting likes for article id: {}", articleId);
        likeRepository.deleteByArticleId(articleId);

        // Delete favorites
        logger.info("Deleting favorites for article id: {}", articleId);
        favoriteRepository.deleteByArticleId(articleId);

        // Delete article
        logger.info("Deleting article id: {}", articleId);
        articleRepository.delete(article);
    }
    public Article updateArticle(Long articleId, String title, String content) {
        Article article = articleRepository.findById(Math.toIntExact(articleId))
                .orElseThrow(() -> new IllegalArgumentException("文章不存在"));

        // 更新标题和内容
        article.setTitle(title);
        article.setContent(content);

        // 保存更新后的文章
        return articleRepository.save(article);
    }
    // 获取热门文章
    public List<Article> getPopularArticlesByUserEmail(String email) {
        return articleRepository.findPopularArticlesByUserEmail(email);
    }
    // 点赞文章
    public Article likeArticle(Long articleId) {
        // 获取文章
        Article article = articleRepository.findById(articleId);
        if (article != null) {
            // 增加点赞数
            article.setLikesCount(article.getLikesCount() + 1);
            // 更新文章
            return articleRepository.save(article);
        }
        return null;
    }
    public Article findById(Long id) {
        return articleRepository.findById(id);
    }



    // 点赞文章
    public Article likeArticle(Long articleId, String email) {
        // 查找文章和用户
        Article article = articleRepository.findById(articleId);

        User user = userRepository.findByEmail(email);

        // 检查该用户是否已点赞该文章
        Optional<Like> existingLike = likeRepository.findByArticleIdAndUserEmail(articleId, email);

        if (existingLike.isPresent()) {
            // 用户已点赞，取消点赞
            likeRepository.delete(existingLike.get());
            article.setLikesCount(article.getLikesCount() - 1); // 更新点赞数
        } else {
            // 用户未点赞，进行点赞
            Like like = new Like();
            like.setArticle(article);
            like.setUser(user);
            like.setCreatedAt(new Timestamp(System.currentTimeMillis()));
            like.setEmail(email);
            System.out.println(email);
            likeRepository.save(like);
            article.setLikesCount(article.getLikesCount() + 1); // 更新点赞数
        }

        // 保存文章（点赞数更新）
        return articleRepository.save(article);
    }

    // 获取文章点赞数
    public int getLikesCount(Long articleId) {
        return likeRepository.countByArticleId(articleId);
    }
    // 判断用户是否已收藏该文章
    public boolean isArticleFavoritedByUser(Article article, User user) {
        // 使用 FavoriteRepository 查找用户是否已经收藏该文章
        return favoriteRepository.existsByArticleAndUser(article, user);
    }

    // 用户收藏文章
    public void addFavorite(Article article, User user) {
        if (!isArticleFavoritedByUser(article, user)) {
            Favorite favorite = new Favorite();
            favorite.setArticle(article);
            favorite.setUser(user);
            favorite.setCreatedAt(new Timestamp(System.currentTimeMillis()));
            favorite.setEmail(user.getEmail());

            favoriteRepository.save(favorite);
            article.setFavoritesCount(article.getFavoritesCount() + 1);  // 更新收藏数
            articleRepository.save(article);
        }
    }

    // 用户取消收藏文章
    public void removeFavorite(Article article, User user) {
        if (isArticleFavoritedByUser(article, user)) {
            Favorite favorite = favoriteRepository.findByArticleAndUser(article, user);
            favoriteRepository.delete(favorite);
            article.setFavoritesCount(article.getFavoritesCount() - 1);  // 更新收藏数
            articleRepository.save(article);
        }
    }
}


