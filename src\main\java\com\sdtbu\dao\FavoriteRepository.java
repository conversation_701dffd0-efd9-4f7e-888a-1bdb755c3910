package com.sdtbu.dao;

import com.sdtbu.pojo.Article;
import com.sdtbu.pojo.Favorite;
import com.sdtbu.pojo.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FavoriteRepository extends JpaRepository<Favorite, Long> {

    

    Integer findByArticleId(Long articleId);

    Integer countByArticleId(Long articleId);

    Favorite findByArticleAndUser(Article article, User user);

    boolean existsByArticleAndUser(Article article, User user);

    List<Favorite> findByEmail(String email);

    void deleteByArticleId(Long articleId);
}

