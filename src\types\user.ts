// 用户相关类型定义

export interface User {
  id: number;
  username: string;
  email: string;
  password?: string;
  avatar: string;
  bio: string;
  createdAt: string;
}

export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  captcha: string;
}

export interface CaptchaLoginForm {
  email: string;
  captcha: string;
}

export interface UpdateUserForm {
  email: string;
  nickname: string;
  bio: string;
  avatar?: File;
}

export interface ChangePasswordForm {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

export interface VerificationCode {
  id: number;
  email: string;
  code: string;
  createdAt: string;
  expiresAt: string;
}

export interface Achievement {
  articlesCount: number;
  totalLikes: number;
  totalComments: number;
  totalFavorites: number;
}
