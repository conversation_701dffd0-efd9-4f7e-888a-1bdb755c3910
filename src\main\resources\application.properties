spring.application.name=Blog-Springboot
# application.properties
server.port=8080
spring.datasource.url=****************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=wuyuhao123456#
spring.jpa.show-sql=true

spring.mail.host=smtp.qq.com
spring.mail.username=<EMAIL>
spring.mail.password=wmlxpdnfkektbaff
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true


spring.ai.openai.api-key=sk-lleozqhffqyvzlrfypagnsdfkssewmtjsvcfrpmspokgxadw
spring.ai.openai.chat.base-url=https://api.siliconflow.cn/
spring.ai.openai.chat.options.model=Qwen/Qwen3-8B

upload.dir=./img
