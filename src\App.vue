<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  HomeOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 当前选中的菜单项
const selectedKeys = ref<string[]>([])

// 监听路由变化，更新选中的菜单项
router.afterEach((to) => {
  selectedKeys.value = [to.name as string]
})

// 登出处理
const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}

// 导航菜单项
const menuItems = computed(() => {
  const items = [
    {
      key: 'home',
      icon: h(HomeOutlined),
      label: '首页',
      path: '/'
    },
    {
      key: 'about',
      icon: h(InfoCircleOutlined),
      label: '关于',
      path: '/about'
    }
  ]

  if (userStore.isLoggedIn) {
    items.push({
      key: 'profile',
      icon: h(UserOutlined),
      label: '个人中心',
      path: '/profile'
    })
  }

  return items
})
</script>

<template>
  <a-layout class="app-layout">
    <!-- 顶部导航栏 -->
    <a-layout-header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <h1 class="app-title">用户管理系统</h1>
        </div>

        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="horizontal"
          class="nav-menu"
        >
          <a-menu-item
            v-for="item in menuItems"
            :key="item.key"
            @click="router.push(item.path)"
          >
            <component :is="item.icon" />
            {{ item.label }}
          </a-menu-item>
        </a-menu>

        <div class="user-section">
          <template v-if="userStore.isLoggedIn">
            <a-dropdown>
              <a-button type="text" class="user-btn">
                <a-avatar :size="32" :src="userStore.user?.avatar">
                  <template #icon>
                    <UserOutlined />
                  </template>
                </a-avatar>
                <span class="username">{{ userStore.user?.username }}</span>
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="router.push('/profile')">
                    <UserOutlined />
                    个人中心
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="handleLogout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
          <template v-else>
            <a-space>
              <a-button @click="router.push('/login')">
                <LoginOutlined />
                登录
              </a-button>
              <a-button type="primary" @click="router.push('/register')">
                注册
              </a-button>
            </a-space>
          </template>
        </div>
      </div>
    </a-layout-header>

    <!-- 主要内容区域 -->
    <a-layout-content class="app-content">
      <RouterView />
    </a-layout-content>

    <!-- 底部 -->
    <a-layout-footer class="app-footer">
      <div class="footer-content">
        <p>&copy; 2024 用户管理系统. All rights reserved.</p>
      </div>
    </a-layout-footer>
  </a-layout>
</template>

<style scoped>
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 64px;
  line-height: 64px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo-section {
  flex-shrink: 0;
}

.app-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 64px;
}

.nav-menu {
  flex: 1;
  border-bottom: none;
  background: transparent;
  margin: 0 24px;
}

.nav-menu .ant-menu-item {
  border-bottom: 2px solid transparent;
  margin: 0 8px;
}

.nav-menu .ant-menu-item-selected {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.user-section {
  flex-shrink: 0;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  padding: 0 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-btn:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #1f2937;
}

.app-content {
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
  padding: 0;
}

.app-footer {
  background: #fff;
  border-top: 1px solid #e8e8e8;
  text-align: center;
  padding: 24px 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-content p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .nav-menu {
    display: none;
  }

  .app-title {
    font-size: 18px;
  }

  .username {
    display: none;
  }
}
</style>
