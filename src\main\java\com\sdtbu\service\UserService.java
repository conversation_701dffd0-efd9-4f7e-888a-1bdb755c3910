package com.sdtbu.service;

import com.sdtbu.dao.ArticleRepository;
import com.sdtbu.dao.FavoriteRepository;
import com.sdtbu.dao.LikeRepository;
import com.sdtbu.dao.UserRepository;
import com.sdtbu.pojo.*;
import com.sdtbu.utils.EncryptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ArticleRepository articleRepository;
    @Autowired
    private LikeRepository likeRepository;
    @Autowired
    private FavoriteRepository favoriteRepository;



    public User register(String email, String password) {
        try {
            // 加密密码
            String encryptedPassword = EncryptionUtil.encrypt(password);

            // 生成 username，假设使用邮箱前缀
            String username = email.split("@")[0];

            // 创建用户对象
            User user = new User();
            user.setEmail(email);
            user.setPassword(encryptedPassword); // 存储加密后的密码
            user.setUsername(username);
            user.setAvatar("/img/avatar0" + (new java.util.Random().nextInt(3) + 1) + ".png");
            user.setBio("这是"+username+"的博客");
            Date now = new Date();
            Timestamp timestamp = new Timestamp(now.getTime());
            user.setCreatedAt(timestamp);

            return userRepository.save(user);
        } catch (Exception e) {
            throw new RuntimeException("注册失败：密码加密出错", e);
        }
    }

    public User login(String email, String password) {
        try {
            User user = userRepository.findByEmail(email);
            if (user == null) {
                throw new RuntimeException("邮箱或密码错误");
            }

            // 解密数据库中存储的密码并比对
            String decryptedPassword = EncryptionUtil.decrypt(user.getPassword());
            if (!password.equals(decryptedPassword)) {
                throw new RuntimeException("邮箱或密码错误");
            }
            return user;
        } catch (Exception e) {
            throw new RuntimeException("登录失败：密码解密出错", e);
        }
    }
    public User findByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    public void updateUser(User user) {
        userRepository.save(user);
    }

    public Achievement getUserAchievements(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        Achievement achievement = new Achievement();
        achievement.setTotalArticles((int) articleRepository.countByUserEmail(email));
        achievement.setTotalLikes(articleRepository.countTotalLikesByUserEmail(email));
        achievement.setTotalComments(articleRepository.countTotalCommentsByUserEmail(email));
        achievement.setTotalFavorites(articleRepository.countTotalFavoritesByUserEmail(email));

        return achievement;
    }

    public List<Article> getLikedArticlesByEmail(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            return List.of();
        }
        List<Like> likes = likeRepository.findByUser(user);
        return likes.stream()
                .map(Like::getArticle)
                .collect(Collectors.toList());
    }

    public List<Article> getFavoriteArticlesByEmail(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            return List.of();
        }
        List<Favorite> favorites = favoriteRepository.findByEmail(email);
        return favorites.stream()
                .map(Favorite::getArticle)
                .collect(Collectors.toList());
    }
}
